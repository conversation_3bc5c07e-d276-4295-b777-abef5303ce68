import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/my_arena/my_arena_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class MyArenaPage extends StatelessWidget {
  MyArenaPage({super.key});

  final logic = Get.put(MyArenaLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.my_site),
      ),
      body: Obx(
        () => Column(
          children: [
            SizedBox(
              height: 15.w,
            ),
            logic.init.value
                ? Expanded(
                    child: NotificationListener(
                        onNotification: (ScrollNotification note) {
                          if (note.metrics.pixels ==
                              note.metrics.maxScrollExtent) {
                            logic.loadMore();
                          }
                          return true;
                        },
                        child: RefreshIndicator(
                          onRefresh: logic.onRefresh,
                          child: Builder(builder: (context) {
                            return Obx(
                              () => CustomScrollView(
                                slivers: [
                                  if (logic.allArenaList.isNotEmpty)
                                    _allArenalist()
                                ],
                              ),
                            );
                          }),
                        )),
                  )
                : buildLoad(),
          ],
        ),
      ),
    );
  }

  SliverList _allArenalist() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        final model = logic.allArenaList[index];
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => AppPage.to(Routes.siteDetailPage, arguments: {
            "id": model.arenaID,
          }),
          child: Padding(
            padding: EdgeInsets.only(bottom: 15.w, left: 15.w, right: 15.w),
            child: Stack(children: [
              Container(
                padding: EdgeInsets.all(15.w),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.r),
                          child: CachedNetworkImage(
                            imageUrl: model.logo ?? '',
                            width: 96.w,
                            height: 96.w,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(left: 15.w),
                            child: SizedBox(
                              height: 96.w,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 5.w,
                                  ),
                                  Text(
                                    maxLines: 2,
                                    model.arenaName ?? '',
                                    style: TextStyles.semiBold14,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(
                                    height: 15.w,
                                  ),
                                  Text(
                                    (model.floorCondition ?? '')
                                        .split('@')
                                        .whereType<String>()
                                        .join(' | '),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyles.display12.copyWith(
                                      color: Colours.color5C5C6E,
                                    ),
                                  ),
                                  const Spacer(),
                                  Row(
                                    children: [
                                      WxAssets.images.icLocation
                                          .image(width: 14, height: 14),
                                      const SizedBox(
                                        width: 5,
                                      ),
                                      Text(
                                        "距离您${model.distance}km",
                                        style: TextStyles.display12,
                                      )
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (model.pedestrianFlowStatus == 1)
                      Row(
                        children: [
                          Text(
                            '当前场馆人流量：',
                            style: TextStyles.display12,
                          ),
                          Container(
                            width: 10.w,
                            height: 10.w,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _getFlowColor(
                                  model.pedestrianFlowStatus ?? 0)['color'],
                            ),
                          ),
                          SizedBox(
                            width: 5.w,
                          ),
                          Text(
                              _getFlowColor(
                                  model.pedestrianFlowStatus ?? 0)['title'],
                              style: TextStyles.display12.copyWith(
                                  color: _getFlowColor(
                                      model.pedestrianFlowStatus ??
                                          0)['color'])),
                        ],
                      ).marginOnly(top: 15.w)
                  ],
                ),
              ),
            ]),
          ),
        );
      },
      childCount: logic.allArenaList.length,
    ));
  }

  Map<String, dynamic> _getFlowColor(int status) {
    switch (status) {
      case 0:
        return {'title': '未知', 'color': Colours.color5C5C6E};
      case 1:
        return {'title': '空闲', 'color': Colours.color15B200};
      case 2:
        return {'title': '稀疏', 'color': Colours.color15B200};
      case 3:
        return {'title': '适中', 'color': Colours.color82B200};
      case 4:
        return {'title': '拥挤', 'color': Colours.colorFB8E00};
      case 5:
        return {'title': '爆满', 'color': Colours.colorFF3F3F};
      default:
        return {'title': '未知', 'color': Colours.color5C5C6E};
    }
  }
}
