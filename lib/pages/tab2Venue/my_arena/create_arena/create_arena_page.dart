import 'dart:developer' as cc;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/my_arena/create_arena/create_arena_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/widgets/custom_calendar_date_picker.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/time_picker_bottom_sheet.dart';
import 'package:shoot_z/widgets/upload_image/image_upload_widget.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class CreateArenaPage extends StatelessWidget {
  CreateArenaPage({super.key});

  final logic = Get.put(CreateArenaLogic());
  final bool showBuy = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('创建场地'),
      ),
      body: _createBattleWidget(context),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: () {
              logic.createChallenge();
            },
            child: Container(
              width: double.infinity,
              height: 50.w,
              alignment: Alignment.center,
              margin: EdgeInsets.only(
                left: 15.w,
                right: 15.w,
              ),
              decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25.r)),
              child: Text(
                '确认创建',
                style: TextStyles.semiBold14,
              ),
            ),
          ),
          SizedBox(
            height: 20.w,
          ),
          SafeArea(
            bottom: true,
            child: Text(
              '创建后需等待系统审核，审核通过后方可生效',
              style: TextStyles.display12,
            ),
          ),
        ],
      ),
    );
  }

  String formatChineseTime(String timeStr) {
    return timeStr.replaceAll("时", ":").replaceAll("分", "");
  }

  /// 列表数据
  _createBattleWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const TextWithIcon(title: '场地位置'),
          SizedBox(
            height: 15.w,
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(15.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '选择场地在地图大致位置',
                  style: TextStyles.semiBold14,
                ),
                SizedBox(
                  height: 15.w,
                ),
                Container(
                  width: double.infinity,
                  height: 50.w,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  decoration: BoxDecoration(
                    color: Colours.color0F0F16,
                    borderRadius: BorderRadius.all(Radius.circular(25.r)),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '选择定位',
                        style: TextStyles.display14
                            .copyWith(color: Colours.color5C5C6E),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 14.sp,
                        color: Colours.white,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 20.w,
          ),
          const TextWithIcon(title: '场地信息'),
          SizedBox(
            height: 15.w,
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(15.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '场地名称',
                      style: TextStyles.semiBold14,
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Text(
                      '请结合场地定位命名，如:中电1期篮球场',
                      style: TextStyles.display12
                          .copyWith(color: Colours.colorA8A8BC),
                    )
                  ],
                ),
                SizedBox(
                  height: 15.w,
                ),
                Container(
                  width: double.infinity,
                  height: 50.w,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  decoration: BoxDecoration(
                    color: Colours.color0F0F16,
                    borderRadius: BorderRadius.all(Radius.circular(25.r)),
                  ),
                  child: TextField(
                    controller: logic.addressController,
                    style: TextStyles.regular,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(15),
                    ],
                    decoration: InputDecoration(
                      hintText: '请输入场地名称',
                      hintStyle: TextStyles.regular
                          .copyWith(color: Colours.color5C5C6E),
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      //让文字垂直居中,
                      border: InputBorder.none,
                    ),
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
                Text(
                  '场地封面',
                  style: TextStyles.semiBold14,
                ),
                SizedBox(
                  height: 15.w,
                ),
                ImageUploadWidget(
                  maxImages: 1,
                  tag: 'logo_upload',
                  onImagesChanged: _handleLogoImagesChanged,
                ),
                // WxAssets.images.logoSelectIcon.image(),
                SizedBox(
                  height: 20.w,
                ),
                Text(
                  '场地类型',
                  style: TextStyles.semiBold14,
                ),
                SizedBox(
                  height: 15.w,
                ),
                Wrap(
                  spacing: 40.w,
                  runSpacing: 20.w,
                  children: List.generate(logic.siteType.length, (index) {
                    return TextButton(
                      onPressed: () {},
                      style: TextButton.styleFrom(
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        padding: EdgeInsets.zero,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          WxAssets.images.unselectIcon.image(),
                          SizedBox(width: 6.w),
                          Text(logic.siteType[index]['title'],
                              style: TextStyles.display14),
                        ],
                      ),
                    );
                  }),
                ),
                SizedBox(
                  height: 20.w,
                ),
                Row(
                  children: [
                    Text(
                      '场地半场',
                      style: TextStyles.semiBold14,
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Text(
                      '请根据场地实际半场位置上传对应的图片',
                      style: TextStyles.display12
                          .copyWith(color: Colours.colorA8A8BC),
                    )
                  ],
                ),
                SizedBox(
                  height: 20.w,
                ),
                WxButton(
                  backgroundColor: Colours.color0F0F16,
                  borderRadius: BorderRadius.circular(20.w),
                  borderSide: BorderSide(color: Colours.white, width: 1.w),
                  height: 40.w,
                  text: '添加场地',
                  textStyle: TextStyles.semiBold14,
                  onPressed: () {
                    logic.halfCourtPics.value.add([]);
                    logic.halfCourtPics.refresh();
                  },
                ),
                SizedBox(
                  height: 20.w,
                ),
                ...List.generate(logic.halfCourtPics.length, (index) {
                  var serialNum1 = (index) ~/ 2 + 1;
                  var serialNum2 = (index) % 2 == 0 ? 1 : 2;
                  return Column(
                    children: [
                      Row(
                        children: [
                          Text(
                            '半场$serialNum1-$serialNum2展示图',
                            style: TextStyles.semiBold14,
                          ),
                          SizedBox(
                            width: 5.w,
                          ),
                          Text(
                            '最多上传3张',
                            style: TextStyles.display12
                                .copyWith(color: Colours.colorA8A8BC),
                          ),
                          if (index != 0)
                            InkWell(
                                onTap: () {
                                  logic.halfCourtPics.removeAt(index);
                                },
                                child: WxAssets.images.icDelete
                                    .image(
                                        color: Colors.red,
                                        width: 16.w,
                                        height: 16.w)
                                    .marginOnly(left: 10.w))
                        ],
                      ),
                      SizedBox(
                        height: 15.w,
                      ),
                      ImageUploadWidget(
                        maxImages: 3,
                        tag: 'half_court_1-1',
                        onImagesChanged: _handleGalleryImagesChanged,
                      ),
                      if (index != logic.halfCourtPics.length - 1)
                        SizedBox(
                          height: 15.w,
                        ),
                    ],
                  );
                }),
              ],
            ),
          ),
          SizedBox(
            height: 20.w,
          ),
          const TextWithIcon(title: '场地其他信息'),
          SizedBox(
            height: 15.w,
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(15.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '场地类型',
                  style: TextStyles.semiBold14,
                ),
                SizedBox(
                  height: 20.w,
                ),
                Wrap(
                  spacing: 40.w,
                  runSpacing: 20.w,
                  children: List.generate(logic.siteCategory.length, (index) {
                    return TextButton(
                      onPressed: () {},
                      style: TextButton.styleFrom(
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        padding: EdgeInsets.zero,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          WxAssets.images.unselectIcon.image(),
                          SizedBox(width: 6.w),
                          Text(logic.siteCategory[index]['title'],
                              style: TextStyles.display14),
                        ],
                      ),
                    );
                  }),
                ),
                SizedBox(
                  height: 20.w,
                ),
                const Divider(
                  color: Colours.color1AFFFFFF,
                  height: 0,
                  thickness: 0.5,
                ),
                SizedBox(
                  height: 20.w,
                ),
                Text(
                  '开放时间',
                  style: TextStyles.semiBold14,
                ),
                SizedBox(
                  height: 20.w,
                ),
                Wrap(
                  spacing: 30.w,
                  runSpacing: 20.w,
                  children: List.generate(logic.openTime.length, (index) {
                    return TextButton(
                      onPressed: () {},
                      style: TextButton.styleFrom(
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        padding: EdgeInsets.zero,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          WxAssets.images.unselectIcon.image(),
                          SizedBox(width: 6.w),
                          Text(logic.openTime[index]['title'],
                              style: TextStyles.display14),
                        ],
                      ),
                    );
                  }),
                ),
                SizedBox(
                  height: 20.w,
                ),
                const Divider(
                  color: Colours.color1AFFFFFF,
                  height: 0,
                  thickness: 0.5,
                ),
                SizedBox(
                  height: 20.w,
                ),
                Text(
                  '有无灯光',
                  style: TextStyles.semiBold14,
                ),
                SizedBox(
                  height: 20.w,
                ),
                Wrap(
                  spacing: 40.w,
                  runSpacing: 20.w,
                  children: List.generate(logic.lightType.length, (index) {
                    return TextButton(
                      onPressed: () {},
                      style: TextButton.styleFrom(
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        padding: EdgeInsets.zero,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          WxAssets.images.unselectIcon.image(),
                          SizedBox(width: 6.w),
                          Text(logic.lightType[index]['title'],
                              style: TextStyles.display14),
                        ],
                      ),
                    );
                  }),
                ),
                SizedBox(
                  height: 20.w,
                ),
                const Divider(
                  color: Colours.color1AFFFFFF,
                  height: 0,
                  thickness: 0.5,
                ),
                SizedBox(
                  height: 20.w,
                ),
                Text(
                  '是否免费',
                  style: TextStyles.semiBold14,
                ),
                SizedBox(
                  height: 20.w,
                ),
                Wrap(
                  spacing: 40.w,
                  runSpacing: 20.w,
                  children: List.generate(logic.isFree.length, (index) {
                    return TextButton(
                      onPressed: () {},
                      style: TextButton.styleFrom(
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        padding: EdgeInsets.zero,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          WxAssets.images.unselectIcon.image(),
                          SizedBox(width: 6.w),
                          Text(logic.isFree[index]['title'],
                              style: TextStyles.display14),
                        ],
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 20.w,
          ),
          const TextWithIcon(title: '场地周边环境'),
          SizedBox(
            height: 15.w,
          ),
          Container(
              width: double.infinity,
              padding: EdgeInsets.all(15.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.all(Radius.circular(8.r)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        '周边环境',
                        style: TextStyles.semiBold14,
                      ),
                      SizedBox(
                        width: 5.w,
                      ),
                      Text(
                        '最多上传3张',
                        style: TextStyles.display12
                            .copyWith(color: Colours.colorA8A8BC),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 15.w,
                  ),
                  ImageUploadWidget(
                    maxImages: 3,
                    tag: 'gallery_upload',
                    onImagesChanged: _handleGalleryImagesChanged,
                  ),
                ],
              )),
        ],
      ).marginSymmetric(horizontal: 15.w, vertical: 15),
    );
  }

  void _handleLogoImagesChanged(List<String> images) {
    // 处理logo图片变化
    print('Logo images changed: $images');
  }

  void _handleGalleryImagesChanged(List<String> images) {
    // 处理相册图片变化
    print('Gallery images changed: $images');
  }

  Widget _cellInfo(String leftTitle,
      {bool showLine = true,
      bool showStar = true,
      TextEditingController? controller,
      String hintText = '请选择',
      bool controllerReadOnly = true,
      BuildContext? context,
      int maxNumber = 0}) {
    DateTime selectDate = DateTime.now();
    return Container(
      decoration: BoxDecoration(
          border: showLine
              ? Border(
                  bottom: BorderSide(color: Colours.color99292937, width: 1.w))
              : null),
      height: 54,
      child: Row(
        children: [
          Opacity(
              opacity: showStar ? 1.0 : 0,
              child: Text(
                '*',
                style: TextStyle(fontSize: 14.sp, color: Color(0xFFFF3F3F)),
              )),
          SizedBox(
            width: 2.w,
          ),
          Text(
            leftTitle,
            style: TextStyles.regular,
          ),
          SizedBox(
            width: 30.w,
          ),
          if (controller != null)
            Expanded(
                child: InkWell(
              onTap: controllerReadOnly
                  ? () async {
                      cc.log("TextField clicked: $leftTitle");
                      if (leftTitle == '我的球队') {
                        AppPage.to(Routes.teamListPage,
                            arguments: {"selectTeam": true}).then((v) {
                          if (v != null) {
                            logic.teamId = v.id;
                          }
                        });
                      } else if (leftTitle == '约战日期') {
                        if (context == null) return;
                        // 处理日期选择
                        DateTime currentDate = DateTime.now();
                        DateTime threeYearsLater =
                            currentDate.copyWith(year: currentDate.year + 3);
                        final dateResult = await showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return CustomCalendarDatePicker(
                                title: '日期选择',
                                firstDay: currentDate,
                                lastDay: threeYearsLater,
                                curFocusedDay: selectDate,
                              );
                            });
                        selectDate = dateResult;
                        String formattedDate =
                            DateFormat('yyyy-MM-dd').format(dateResult);
                      } else if (leftTitle == '开始时间') {
                        if (context == null) return;
                        // 处理时间选择
                        final result = await showModalBottomSheet(
                            context: context,
                            builder: (BuildContext context) {
                              return TimePickerBottomSheet();
                            });
                      } else if (leftTitle == '约战地点') {
                        // 处理地点选择
                        _showVenueListBottomSheet(context);
                      }
                    }
                  : null,
              child: AbsorbPointer(
                absorbing: controllerReadOnly,
                child: TextField(
                  controller: controller,
                  readOnly: controllerReadOnly,
                  style: TextStyles.regular,
                  inputFormatters: maxNumber == 0
                      ? null
                      : [
                          LengthLimitingTextInputFormatter(maxNumber),
                        ],
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                    contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                    //让文字垂直居中,
                    border: InputBorder.none,
                  ),
                ),
              ),
            )),
          if (controllerReadOnly && controller != null)
            Icon(
              Icons.arrow_forward_ios,
              size: 14.w,
              color: Colours.color5C5C6E,
            )
        ],
      ),
    );
  }

  Widget _optionsWidget(List options, RxInt selectIndex) {
    return SizedBox(
      width: ScreenUtil().screenWidth - 60.w,
      child: Obx(() {
        return Wrap(
          spacing: 9.w,
          children: List.generate(
              options.length,
              (index) => GestureDetector(
                    onTap: () {
                      selectIndex.value = index + 1;
                    },
                    child: Container(
                      width: (ScreenUtil().screenWidth - 36.w - 60.w) / 4.0,
                      height: 32.w,
                      decoration: BoxDecoration(
                          gradient: selectIndex.value == index + 1
                              ? const LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                )
                              : null,
                          border: selectIndex.value == index + 1
                              ? null
                              : Border.all(color: Colors.white, width: 1),
                          borderRadius: BorderRadius.circular(16.r)),
                      child: Center(
                        child: Text(
                          options[index],
                          style: TextStyles.display12
                              .copyWith(color: Colors.white),
                        ),
                      ),
                    ),
                  )),
        );
      }),
    );
  }

  /// 显示场馆列表底部弹窗
  void _showVenueListBottomSheet(BuildContext? context) {
    if (context == null) return;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: Column(
            children: [
              // 顶部指示条
              Container(
                width: 38.w,
                height: 3.w,
                margin: EdgeInsets.only(top: 6.w),
                decoration: BoxDecoration(
                  color: Colours.color1AD8D8D8,
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),

              // 标题栏
              Padding(
                padding: EdgeInsets.symmetric(vertical: 18.w),
                child: Text(
                  '选择场馆',
                  style: TextStyles.semiBold.copyWith(fontSize: 16.sp),
                ),
              ),

              SizedBox(height: 15.w),

              // 场馆列表
              Expanded(
                  child: Obx(
                () => !logic.init.value
                    ? const Center(
                        child: CupertinoActivityIndicator(
                        color: Colors.white,
                      ))
                    : _list(context),
              )),

              // 底部确认按钮
              Container(
                padding: EdgeInsets.only(
                    top: 10,
                    left: 15,
                    right: 15,
                    bottom: ScreenUtil().bottomBarHeight + 8),
                child: InkWell(
                  onTap: () {
                    // 确认选择
                    if (logic.selectArenaName.value.isNotEmpty) {
                      logic.addressController.text =
                          logic.selectArenaName.value;
                      Navigator.pop(context);
                    } else {
                      // 提示用户选择场馆
                      WxLoading.showToast('请先选择一个场馆');
                    }
                  },
                  child: Obx(() => Container(
                        width: double.infinity,
                        height: 50.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          gradient: logic.selectArenaName.value.isNotEmpty
                              ? const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.bottomLeft,
                                  end: Alignment.bottomRight,
                                )
                              : null,
                          color: logic.selectArenaName.value.isEmpty
                              ? Colours.color3A3A42
                              : null,
                          borderRadius: BorderRadius.circular(25.r),
                        ),
                        child: Text(
                          '确认',
                          style: TextStyles.semiBold14.copyWith(
                            color: logic.selectArenaName.value.isNotEmpty
                                ? Colors.white
                                : Colours.color999999,
                          ),
                        ),
                      )),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _list(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(
            top: 0, bottom: MediaQuery.of(context).padding.bottom),
        itemCount: logic.arenaList.isEmpty
            ? 1
            : (logic.searchText.value.isEmpty
                ? logic.arenaList.length + 1
                : logic.arenaList.length),
        itemBuilder: (context, index) {
          if (logic.arenaList.isNotEmpty) {
            if (index == logic.arenaList.length) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  WxAssets.images.tips.image(width: 11.5.w, fit: BoxFit.fill),
                  SizedBox(
                    width: 4.w,
                  ),
                  Text(
                    '距您15km以外的球馆请在顶部搜索栏进行搜索',
                    style: TextStyles.regular
                        .copyWith(fontSize: 10.sp, color: Colours.color5C5C6E),
                  )
                ],
              );
            }
            return _modelItem(context, index);
          }
          return _empty();
        });
  }

  Widget _modelItem(BuildContext context, int index) {
    final model = logic.arenaList[index];
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        logic.selectArenaName.value = model.arenaName ?? '';
        logic.selectArenaId.value = model.arenaID.toString();
        cc.log("Selected venue: ${model.arenaName}");
      },
      child: Container(
        margin: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 15.w),
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
          color: Colours.color22222D,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(children: [
                CachedNetworkImage(
                  imageUrl: model.logo ?? '',
                  width: 86.w,
                  height: 86.w,
                  fit: BoxFit.cover,
                ),
                if ((model.type ?? 0) > 0)
                  Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: 25.w,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(top: 4.w),
                        decoration: BoxDecoration(
                          image: DecorationImage(
                              image: WxAssets.images.icHlItemBottom.provider(),
                              fit: BoxFit.fill),
                        ),
                        child: Text(
                          '',
                          style: TextStyles.display10,
                        ),
                      )),
              ]),
            ),
            SizedBox(
              width: 15.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.arenaName ?? '',
                    style: TextStyles.semiBold,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(
                    height: 14.w,
                  ),
                  Row(
                    children: [
                      WxAssets.images.icLocation
                          .image(width: 12.w, height: 12.w),
                      SizedBox(
                        width: 3.w,
                      ),
                      Expanded(
                        child: Text(
                          model.address ?? '',
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp, color: Colours.color5C5C6E),
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 14.w,
                  ),
                  Text(
                    "距你${model.distance}km",
                    style:
                        TextStyles.regular.copyWith(color: Colours.colorA44EFF),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 14.w,
            ),
            Obx(() => logic.selectArenaId.value == model.arenaID.toString()
                ? WxAssets.images.selectIcon.image()
                : WxAssets.images.unselectIcon.image())
          ],
        ),
      ),
    );
  }

  Widget _empty() {
    return Container(
      padding: EdgeInsets.only(top: 80.w),
      child: Column(
        children: [
          WxAssets.images.icSearchNo.image(width: 152.w, height: 155.w),
          SizedBox(
            height: 30.w,
          ),
          Text(
            "暂无搜索结果",
            style: TextStyles.display16,
          )
        ],
      ),
    );
  }
}
