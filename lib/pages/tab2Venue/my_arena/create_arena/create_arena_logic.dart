import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CreateArenaLogic extends GetxController with WidgetsBindingObserver {
  List siteType = [
    {'title': '室内', 'id': '0'},
    {'title': '室外', 'id': '1'}
  ];
  var siteTypeSelectIndex = (-1).obs;
  List siteCategory = [
    {'title': '木地板', 'id': '0'},
    {'title': '塑胶', 'id': '1'},
    {'title': '悬浮拼接地板', 'id': '2'},
    {'title': '水泥地', 'id': '3'}
  ];
  var siteCategorySelectIndex = (-1).obs;
  List openTime = [
    {'title': '不对外开放', 'id': '0'},
    {'title': '全天开放', 'id': '1'},
    {'title': '白天开放', 'id': '2'},
    {'title': '晚上开放', 'id': '3'}
  ];
  var openTimeSelectIndex = (-1).obs;
  List lightType = [
    {'title': '有灯光', 'id': '0'},
    {'title': '无灯光', 'id': '1'}
  ];
  var lightTypeSelectIndex = (-1).obs;
  List isFree = [
    {'title': '免费', 'id': '0'},
    {'title': '收费', 'id': '1'}
  ];
  var isFreeSelectIndex = (-1).obs;
  var halfCourtPics = [[]].obs;
  TextEditingController addressController = TextEditingController(); //地点
  @override
  void onInit() {
    super.onInit();
    halfCourtPics.value = [[]];
  }

  @override
  void onClose() {
    addressController.dispose();
    super.onClose();
  }

  Future<void> createSite() async {}
}
