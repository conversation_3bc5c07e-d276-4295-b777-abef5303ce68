import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/widgets/upload_image/image_upload_controller.dart';

class CreateArenaLogic extends GetxController with WidgetsBindingObserver {
  List siteType = [
    {'title': '室内', 'id': '0'},
    {'title': '室外', 'id': '1'}
  ];
  var siteTypeSelectIndex = (-1).obs;
  List siteCategory = [
    {'title': '木地板', 'id': '0'},
    {'title': '塑胶', 'id': '1'},
    {'title': '悬浮拼接地板', 'id': '2'},
    {'title': '水泥地', 'id': '3'}
  ];
  var siteCategorySelectIndex = (-1).obs;
  List openTime = [
    {'title': '不对外开放', 'id': '0'},
    {'title': '全天开放', 'id': '1'},
    {'title': '白天开放', 'id': '2'},
    {'title': '晚上开放', 'id': '3'}
  ];
  var openTimeSelectIndex = (-1).obs;
  List lightType = [
    {'title': '有灯光', 'id': '0'},
    {'title': '无灯光', 'id': '1'}
  ];
  var lightTypeSelectIndex = (-1).obs;
  List isFree = [
    {'title': '免费', 'id': '0'},
    {'title': '收费', 'id': '1'}
  ];
  var isFreeSelectIndex = (-1).obs;
  var halfCourtPics = [
    {'title': '1-1', 'pics': []}
  ].obs;
  var logoPics = [].obs;
  var envPics = [].obs;
  TextEditingController siteNameController = TextEditingController(); //地点
  var siteNameStr = ''.obs;
  var addressName = "".obs;
  // 表单验证状态
  RxBool isFormValid = false.obs;
  @override
  void onInit() {
    super.onInit();
    initFormListeners();
  }

  @override
  void onClose() {
    siteNameController.removeListener(_textChangeListener);
    siteNameController.dispose();
    // 清理所有半场相关的ImageUploadWidget控制器
    cleanupAllHalfCourtControllers();
    super.onClose();
  }

  void _textChangeListener() {
    siteNameStr.value = siteNameController.text;
  }

// 监听所有必填字段的变化
  void initFormListeners() {
    siteNameController.addListener(_textChangeListener);
    everAll([halfCourtPics], (_) {
      log('!!!!!!!!!!!!initFormListeners');
      _validateForm();
    });
  }

  bool checkForEmptyPicArrays(List items) {
    for (var item in items) {
      log('checkForEmptyPicArrays${item['pic']}');

      if (item['pic'] != null) {
        List imagesArr = item['pic'];
        return imagesArr.isNotEmpty;
      }
    }
    return false;
  }

  // 验证表单
  void _validateForm() {
    log('_validateForm$halfCourtPics');
    final bool isValid = (checkForEmptyPicArrays(halfCourtPics));

    isFormValid.value = isValid;
  }

  /// 清理所有半场相关的ImageUploadWidget控制器
  void cleanupAllHalfCourtControllers() {
    for (int i = 0; i < halfCourtPics.length; i++) {
      var serialNum1 = i ~/ 2 + 1;
      var serialNum2 = i % 2 == 0 ? 1 : 2;
      String tag = 'half_court_$serialNum1-$serialNum2';
      if (Get.isRegistered<ImageUploadController>(tag: tag)) {
        Get.delete<ImageUploadController>(tag: tag);
      }
    }
  }

  Future<void> createSite() async {}
}
