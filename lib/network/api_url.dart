import 'dart:io';

class ApiUrl {
  static const String vipPolicy =
      'https://i.shootz.tech/shared-h5/vipAgreement';
  static String get privacyPolicy {
    if (Platform.isAndroid) {
      return 'https://i.shootz.tech/shared-h5/AndroidPrivacyAgreement';
    }
    return 'https://i.shootz.tech/shared-h5/privacyAgreement';
  }

  static const String userPolicy =
      'https://i.shootz.tech/shared-h5/userAgreement';
  static const String getCode = "/user-api/user/phone-sms";
  static const String wxInit = "/user-api/user/wx-init";
  static const String wxLogin = "/user-api/user/wx-login";
  static const String checkIsBind = "/user-api/user/phone/check-bind";
  static const String login = "/user-api/user/phone-login";
  static const String userInfo = "/user-api/user/info";
  static const String checkOldPhone = "/user-api/user/phone/replace-check-old";
  static const String bindNewPhone = "/user-api/user/phone/replace";
  static const String logout = "/user-api/user/logout";
  static const String commonRelease = "/user-api/common/release"; //查询最新版本
  static const String refreshToken = "/user-api/user/refresh-token";
  static const String getAreaList = "/user-api/common/area"; //获取行政区域
  static const String upload = "/user-api/common/upload-avatar";

  static const String editUser = "/user-api/user";
  static const String deleteAccount = "/user-api/user/account";
  static const String configResource = '/user-api/common/config-resource';
  static const String inviteCode = '/user-api/user/invite-code';
  static const String recommendedStadium = "/arenas-api/arenas/recommend";
  static const String myArenas = "/arenas-api/arenas/my"; //我的主场
  static const String arenasList = "/arenas-api/arenas/list";
  static const String arenaHighlightsList = "/highlights-api/video/arena";
  static const String arenasSearchName = '/arenas-api/arenas/search-name';
  static const String arenasSearchModel = '/arenas-api/arenas/search-list';
  static const String vipRecommendList =
      '/arenas-api/arenas/vip-recommend-list';
  static const String gameList = '/matches-api/matches/date';
  static const String mySummary = '/matches-api/matches/my-summary'; //我的赛事统计
  static const String channelSubmit = '/data-api/channel/submit'; //渠道埋点上传
  static const String getApmTracking = '/data-api/apm/tracking'; //内容埋点上传

  static const String career = '/matches-api/matches/career'; //我的赛事统计
  static const String vipPriceList = '/payment-api/vip/sku'; //查询会员价格
  static const String arenasDetail = "/arenas-api/arenas/detail"; //球馆主页详情
  static const String arenaGameList =
      "/matches-api/matches/arena-matches"; //球馆主页详情 比赛列表

  static const String getFeaturedList =
      "/data-api/shootz/featured-content"; //球秀精选查询列表
  static getAIReport(String matchId) {
    //AI战报
    return "/data-api/ai-report/$matchId/report";
  }

  //订阅AI战报
  static const String subscribeAIReport = "/user-api/common/subscribe";

  static const String pointsAllTask = "/user-api/point/all-task"; //积分 全部任务
  static const String pointsExchangeGoods =
      "/user-api/mini/point/exchange-goods"; //积分 兑换商品
  static const String goodsDetail =
      "/user-api/mini/point/goods-detail"; //积分 兑换商品的详情
  static const String getCommonLocation =
      "/user-api/common/location"; //根据坐标获得地址
  static const String pointsMyPoint = "/user-api/point/my-point"; //积分 我的积分
  static const String pointsSignIn = "/user-api/point/sign-in"; //积分 每日积分签到
  static const String pointsGoodsList =
      "/user-api/point/goods-list"; //积分 积分商城列表
  static const String pointsGoodsCategoryList =
      "/user-api/point/goods-category"; //积分 积分商城商品类别
  static const String pointsIsClockIn =
      "/user-api/point/check-user-clock-in"; //积分 是否已经在球馆打卡
  static const String pointsClockIn =
      "/user-api/point/arena-clock-in"; //积分 球馆打卡
  static const String getShareTask = "/user-api/point/share-task"; //积分 完成分享任务

  // String courts = "/court/:${arenaId}/courts";//球馆中的场地列表
  static const String teachingVideo =
      "/data-api/shootz/teaching-video"; //教学视频列表
  //获得球场的场地信息
  static Future<String> getCourtsUrl(int arenaID) async {
    // WxLoading.show();
    final url = "/arenas-api/court/$arenaID/courts";
    return url;
  }

  //获得球场的场地信息/team/{teamId}/photo
  static Future<String> getTeamIdPhotoUrl(String teamId) async {
    // WxLoading.show();
    final url = "/matches-api/team/$teamId/photo";
    return url;
  }

  static const String userIsNew = "/user-api/user/is-new"; //是否新用户

  //领取会员
  static Future<String> getReceiveVip(String activityId) async {
    // WxLoading.show();
    final url = "/user-api/activity/$activityId/receive";
    return url;
  }

  static const String getCity = "/user-api/common/location"; //查询城市名称

  static const String getCourtVideos =
      "/highlights-api/fragment/source-videos"; //获得球馆中某个场地的视频列表
  static const String getCanDownLoadVideoUrl =
      "/highlights-api/fragment/fragment-download"; //判断视频是否能下载  然后返回最新下载地址
  static const String getClassifications =
      "/highlights-api/fragment/classifications"; //ai 选球查询身型分类
  static const String getClassifications2 =
      "/highlights-api/fragment/player-classifications"; //ai 选球查询身型分类 半场打野
  static const String getClassificationsVideos =
      "/highlights-api/fragment/classification-videos"; //ai 选球查询身型分类下的视频
  static const String getIdsVideos =
      "/highlights-api/fragment/highlights/path"; //通过ids的视频列表
  static const String getMergeVideos = "/highlights-api/merge"; //合成视频
  static const String getMergeVideos2 =
      "/highlights-api/merge/video"; //球场打野合成视频
  static const String getVideosUsedShots =
      "/highlights-api/merge/used-shots"; //查询用户已使用的片段以及是否vip
  static const String getAchievementsVideos =
      "/highlights-api/workspace/achievements"; //查询用户创作集锦

  static const String getCourtVideosRange =
      "/arenas-api/court/video-range"; //获取视频范围
  static const String myHighlights = '/highlights-api/fragment/my-highlights';
  static const String deleteHighlights = '/highlights-api/fragment/highlights/';
  static const String createOrder = '/payment-api/order';
  static const String pay = '/payment-api/payment/pay';
  static const String homeHotRecommend = '/matches-api/matches/hot-recommend';

  static const String homeChallengeList =
      "/matches-api/matches/challenge/list"; //首页约战列表

  static const String getMessageList = "/user-api/message/list"; //消息列表
  static const String getMessageTypeList =
      "/user-api/message/type/list"; //消息类型列表
  static const String getMessagehasUnread =
      "/user-api/message/has_unread"; //消息是否已读
  //消息详情
  static Future<String> getMessageInfo(String mid) async {
    final url = "/user-api/message/$mid";
    return url;
  }

  static const String getAddressList =
      "/user-api/user/address"; //收货地址列表 get获得 post添加
  static Future<String> putAddress(String id) async {
    final url = "/user-api/user/address/$id";
    return url;
  }

  //获得球队详情
  static Future<String> deleteAddress(String id) async {
    final url = "/user-api/user/address/$id";
    return url;
  }

  //我的球队
  static const String myTeamsList = "/matches-api/team/my-teams"; //我的球队列表

  static const String createTeam = "/matches-api/team"; //创建球队
  static const String completeInfo = "/user-api/user/complete-info"; //补全个人信息
  static const String userDetail = "/user-api/user/detail"; //获得球员的用户信息
  static const String getTeamList = "/matches-api/team/list"; //球队队伍列表

  //获得球队详情
  static Future<String> getTeamInfo(String teamId) async {
    final url = "/matches-api/team/$teamId";
    return url;
  }

  //获得球队申请列表
  static Future<String> getTeamApplyList(String teamId) async {
    final url = "/matches-api/team/$teamId/apply-list";
    return url;
  }

  //查询球队球员照片
  static Future<String> getTeamPhotos(String teamId) async {
    final url = "/matches-api/team/$teamId/photo";
    return url;
  }

  //删除球队球员照片
  static Future<String> deleteTeamPhotos(String teamId) async {
    final url = "/matches-api/team/$teamId/photo";
    return url;
  }

  //设置球队封面
  static Future<String> postTeamPhotoCover(String teamId) async {
    final url = "/matches-api/team/$teamId/photo/cover";
    return url;
  }

  //查询球队球员列表
  static Future<String> getTeamPlayers(String teamId) async {
    final url = "/matches-api/team/$teamId/members";
    return url;
  }

  //申请加入球队
  static Future<String> getTeamApply(String teamId) async {
    final url = "/matches-api/team/$teamId/apply";
    return url;
  }

  //修改球队详情
  static Future<String> putUpdateTeamInfo(String teamId) async {
    final url = "/matches-api/team/$teamId";
    return url;
  }

  //获得球员报告详情
  static Future<String> getTeamPlayerReport(
      String machId, String teamId, String playerId) async {
    final url = "/matches-api/matches/$machId/team/$teamId/player/$playerId";
    return url;
  }

  //绑定球员
  static Future<String> getTeamPlayerReportBind(
      String machId, String teamId, String playerId) async {
    final url =
        "/matches-api/matches/$machId/team/$teamId/player/$playerId/join-bind";
    return url;
  }

  //获得赛季播放、分享视频信息 视频片段
  static Future<String> getVideoInfo(String machId) async {
    final url = "/matches-api/matches/$machId/shared-video-info";
    return url;
  }

  //解绑球员
  static Future<String> getTeamPlayerReportUnbind(
      String machId, String teamId, String playerId) async {
    final url =
        "/matches-api/matches/$machId/team/$teamId/player/$playerId/unbind";
    return url;
  }

  //设置默认球队
  static Future<String> putTeamDefault(String teamId) async {
    final url = "/matches-api/team/$teamId/default";
    return url;
  }

  //解散球队
  static Future<String> postTeamDisband(String teamId) async {
    final url = "/matches-api/team/$teamId/disband";
    return url;
  }

  //加入球队
  static Future<String> postTeamApply(String teamId) async {
    final url = "/matches-api/team/$teamId/apply";
    return url;
  }

  //查询球队成员
  static Future<String> getTeamMembers(String teamId) async {
    final url = "/matches-api/team/$teamId/members";
    return url;
  }

  //退出球队
  static Future<String> postTeamQuit(String teamId) async {
    final url = "/matches-api/team/$teamId/quit";
    return url;
  }

  //入队审批
  static Future<String> postTeamApplyAudit(String teamId) async {
    final url = "/matches-api/team/$teamId/apply/audit";
    return url;
  }

  //查询球队赛事
  static Future<String> getTeamSchedule(String teamId) async {
    final url = "/matches-api/team/$teamId/schedule";
    return url;
  }

  //查询球队赛程/team/{teamId}/competitions
  static Future<String> getTeamCompetitions(String teamId) async {
    final url = "/matches-api/team/$teamId/competitions";
    return url;
  }

  //球队主页
  static Future<String> getTeamSummary(String teamId) async {
    final url = "/matches-api/team/$teamId/summary";
    return url;
  }

  //球队数据
  static Future<String> getTeamData(String teamId) async {
    final url = "/matches-api/team/$teamId/data";
    return url;
  }

  //查下球队球员排行
  static Future<String> getTeamPlayerRank(String teamId) async {
    final url = "/matches-api/team/$teamId/player-rank";
    return url;
  }

  //移交队长
  static Future<String> putTeamTransfer(
      String teamId, String targetUserId) async {
    final url = "/matches-api/team/$teamId/transfer/$targetUserId";
    return url;
  }

  //移除队员
  static Future<String> getTeamPlayerOut(String teamId, String memberId) async {
    final url = "/matches-api/team/$teamId/$memberId";
    return url;
  }

  //查询球队视频
  static Future<String> getTeamVideos(String teamId) async {
    final url = "/matches-api/team/$teamId/videos";
    return url;
  }

  //赛事详情
  static String getMatches(String matchId) {
    return "/matches-api/matches/$matchId";
  }

  //分节数据
  static String getSectionScore(String matchId) {
    return "/matches-api/matches/$matchId/section-score";
  }

  //获取球员视频片段
  static String getReadyVideo(String matchId) {
    return "/matches-api/matches/$matchId/ready-video";
  }

  static const String pointList = "/user-api/mini/point/point-list"; //积分明细列表
  static const String pointExchange =
      "/user-api/mini/point/exchange-record"; //积分兑换记录

  static const String gameCouponsList = "/payment-api/coupon/match"; //赛事优惠券列表
  static const String myOrderList = "/payment-api/order/my"; //我的订单
  static const String myCouponsList = "/payment-api/coupon/my-coupons"; //优惠券列表
  static const String couponsHistoryList =
      "/payment-api/coupon/my-coupons-history"; //优惠券历史
  static const String myCouponsHistoryList =
      "/payment-api/coupon/my-coupons-history"; //优惠券历史列表
  static const String orderMatch = '/payment-api/order/match';

  //球队报告
  static String getTeamReport(String matchId) {
    return "/matches-api/matches/$matchId/teams";
  }

  //球员列表
  static String getMatchIdTeamReport(String matchId) {
    return "/matches-api/matches/$matchId/players";
  }

  static String getMatchIdPayInfo(String matchId) {
    return "/matches-api/matches/$matchId/pay-info";
  }

  static String getMatchPlayers(String matchId) {
    return "/matches-api/matches/$matchId/players";
  }

  static String checkScore(String matchId) {
    return "/matches-api/matches/rating/$matchId/check";
  }

  static String submitScore(String matchId) {
    return "/matches-api/matches/rating/$matchId";
  }

  static String highlightsPlay(String id) {
    return "/highlights-api/fragment/highlights/$id/play";
  }

  static String comparison(String matchId) {
    return "/matches-api/matches/$matchId/teams/comparison";
  }

//预约赛事
  static String matchesSubscribe(String matchId) {
    return "/matches-api/matches/$matchId/subscribe";
  }

  //取消预约
  static String matchesCancelSubscribe(String matchId) {
    return "/matches-api/matches/$matchId/unsubscribe";
  }

  static String rankingsPlayers = "/data-api/ranking/match/players";

  static String rankingsTeams = "/data-api/ranking/match/teams";
  static String teamsRanking = "/data-api/ranking/team-rank";
  static String inviteMiniPath =
      "/user-api/activity/invite-mini-path"; //获得分享微信邀请新人参数
  static String obtainNotify = "/payment-api/coupon/obtain-notify"; //优惠券获取通知
  ///半场投篮
  static const String halfShootingRecords =
      "/matches-api/half/shooting/records"; //投篮合并记录列表
  static String halfShootingRecordsId(String id) {
    //投篮记录详情
    return "/matches-api/half/shooting/records/$id";
  }

  //获取自由投篮视频片段
  static String getShootingFragments(String trainingId, String reportId) {
    return "/matches-api/half/shooting/$trainingId/report/$reportId/fragments";
  }

  //半场合成视频
  static Future<String> getShootingvideos(
      String trainingId, String reportId) async {
    final url =
        "/matches-api/half/shooting/$trainingId/records/$reportId/videos";
    return url;
  }

  //半场合成视频的身形数据
  static Future<String> getShootingClassifications(String trainingId) async {
    final url = "/matches-api/half/shooting/$trainingId/classifications";
    return url;
  }

  //创建报告
  static Future<String> getCreateReport(String trainingId) async {
    final url = "/matches-api/half/shooting/$trainingId/report";
    return url;
  }

  //删除半场合成的视频
  static Future<String> getDeleteVideo(String videoId) async {
    final url = "/matches-api/half/shooting/merge-video/$videoId";
    return url;
  }

  static const String halfShootingRecording =
      "/matches-api/half/shooting/recordings"; //开始录制

  static Future<String> halfShootingRecordingEnd(String trainingId) async {
    final url = "/matches-api/half/shooting/$trainingId/recordings";
    return url; //结束录制
  }

  static const String halfShootingVideos =
      "/matches-api/half/shooting/videos"; //半场投篮集锦总视频列表
  static const String halfShootingVideosFragments =
      "/matches-api/half/shooting/videos-fragments"; //片段视频列表
  static const String shootingSample =
      "/matches-api/half/shooting/sample"; //半场示例页面
  static const String sampleVideo =
      "/matches-api/half/shooting/tutorial"; //示例视频
  static Future<String> halfShootingEvents(String trainingId) async {
    final url = "/matches-api/half/shooting/$trainingId/events";
    return url; //添加事件
  }

  static Future<String> halfShootingResource(String trainingId) async {
    final url = "/matches-api/half/shooting/$trainingId/resource";
    return url; //添加事件资源
  }

  static const String reportNew = "/highlights-api/report/new"; //球场的半场打野报告
  static const String classificationReportVideos =
      "/highlights-api/fragment/classification-report-videos"; //球场根据身型分类视频
  static Future<String> recordsDelete(var reportId) async {
    final url = "/matches-api/half/shooting/records/$reportId";
    return url; //半场打野删除报告
  }

  //约战
  static const String myMatchesList =
      "/matches-api/matches/my-challenge/list"; //约战列表
  static const String createChallenge =
      "/matches-api/matches/challenge/create"; //创建约战
  static const String editChallenge =
      "/matches-api/matches/challenge/update"; //修改约战
  static const String createIntention =
      "/matches-api/matches/intention/create"; //提交意向记录
  static const String intentionList =
      "/matches-api/matches/intention/list"; //意向记录列表
  static getChallengeDetail(String challengeId) {
    //约战详情
    return "/matches-api/matches/challenge/$challengeId/info"; //半场打野删除报告
  }

  static const String deleteChallenge =
      "/matches-api/matches/challenge/delete"; //删除约战

  //赛事赛程
  static const String competitionsList =
      "/matches-api/matches/competitions"; //获取赛程列表
  static competitionDetail(int competitionId) {
    //赛程详情
    return "/matches-api/matches/competition/$competitionId";
  }

  static competitionList(int competitionId) {
    //赛事赛程列表
    return "/matches-api/matches/competition/$competitionId/matches";
  }

  static competitionData(int competitionId) {
    //赛程数据
    return "/matches-api/matches/competition/$competitionId/data";
  }

  static teamRankData(int competitionId) {
    //赛程队伍排行榜
    return "/matches-api/matches/competition/$competitionId/team-rank";
  }

  static playerRankData(int competitionId) {
    //赛程队员排行榜
    return "/matches-api/matches/competition/$competitionId/player-rank";
  }

  static competitionSignUp(int competitionId) {
    //赛程报名
    return "/matches-api/matches/competition/$competitionId/registration";
  }

  static competitionTeams(int competitionId) {
    //赛程球队列表
    return "/matches-api/matches/competition/$competitionId/teams";
  }

  static const String myRegistrations =
      "/matches-api/matches/registrations"; //我的报名列表

  static const String myCareerData = "/matches-api/team/me"; //我的生涯
  static const String getMatchCareerList =
      "/matches-api/matches/user/career"; //赛事列表
}
